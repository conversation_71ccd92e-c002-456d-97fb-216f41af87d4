import { useCallback, useEffect, useMemo, useState } from 'react';

import { useAssetChartData } from '@/hooks/useAssetChartData';
import { useAssetData } from '@/hooks/useAssetData';
import { useAssetFiltering } from '@/hooks/useAssetFiltering';
import { useAssetSorting } from '@/hooks/useAssetSorting';
import { applyAssetFilters } from '@/utils/assetTableFiltering';
import { applyAssetSorting } from '@/utils/assetTableSorting';
import {
  findBtcDataForSymbol,
  formatDate,
  processCryptoStatistics,
} from '@/utils/dataProcessors';
import { processStockStatistics } from '@/utils/stockDataProcessors';

import type { CryptoCurrencyStatisticsDto, IndicatorValueDto, StockStatisticsDto } from '@/generated';
import type { AssetType, FilterConfig, SortConfig } from '@/types/table';
import type { AssetStatisticsDto } from '@/utils/assetTableFiltering';

interface UseTableDataManagerProps {
  assetType: AssetType;
}

interface TableDataManagerReturn {
  // Processed data
  processedData: AssetStatisticsDto[];
  btcStatistics: AssetStatisticsDto[];
  totalCount: number;
  filteredCount: number;
  loading: boolean;
  error: string | null;

  // Chart data
  chartData: unknown;
  showChart: boolean;
  setShowChart: (show: boolean) => void;

  // Actions
  onSignalClick: (symbol: string, currency: string) => Promise<void>;
  onRefresh: () => void;

  // Utilities
  formatDate: (date?: string) => string;
  findBtcDataForSymbol: (
    btcStats: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[],
    symbol: string,
  ) => IndicatorValueDto | undefined;
}

/**
 * Process asset statistics based on asset type
 */
const useProcessedAssetData = (
  assetStatistics: AssetStatisticsDto[] | null,
  assetType: AssetType,
) => {
  return useMemo(() => {
    if (!assetStatistics || assetStatistics.length === 0) {
      return [];
    }

    return assetType === 'crypto'
      ? processCryptoStatistics(assetStatistics as CryptoCurrencyStatisticsDto[]).usdStatistics
      : processStockStatistics(assetStatistics as StockStatisticsDto[]).stockStatistics;
  }, [assetStatistics, assetType]);
};

/**
 * Process BTC statistics for both crypto and stock data
 */
const useBtcStatistics = (
  assetStatistics: AssetStatisticsDto[] | null,
  assetType: AssetType,
) => {
  return useMemo(() => {
    if (!assetStatistics || assetStatistics.length === 0) {
      return [];
    }

    return assetType === 'crypto'
      ? processCryptoStatistics(assetStatistics as CryptoCurrencyStatisticsDto[]).btcStatistics
      : processStockStatistics(assetStatistics as StockStatisticsDto[]).btcStatistics;
  }, [assetStatistics, assetType]);
};

interface FilterAndSortConfig {
  processedData: AssetStatisticsDto[];
  btcStatistics: AssetStatisticsDto[];
  filterConfig: FilterConfig;
  sortConfig: SortConfig;
  assetType: AssetType;
}

/**
 * Apply filtering and sorting to processed data
 */
const useFilteredAndSortedData = (config: FilterAndSortConfig) => {
  return useMemo(() => {
    if (config.processedData.length === 0) {
      return [];
    }

    const filtered = applyAssetFilters(config.processedData, config.btcStatistics, config.filterConfig);
    return applyAssetSorting(filtered, config.btcStatistics, config.sortConfig, config.assetType);
  }, [config.processedData, config.btcStatistics, config.filterConfig, config.sortConfig, config.assetType]);
};

/**
 * Unified hook that manages all table data logic for both crypto and stock
 * Now uses generic hooks to eliminate duplication
 */
export const useTableDataManager = ({
  assetType
}: UseTableDataManagerProps): TableDataManagerReturn => {
  const [showChart, setShowChart] = useState(false);

  // Generic hooks that work for both crypto and stock
  const { data: assetStatistics, loading, error, fetchData } = useAssetData(assetType);
  const { chartData, fetchChartData } = useAssetChartData(assetType);
  const { filterConfig } = useAssetFiltering();
  const { sortConfig } = useAssetSorting();

  // Process data using extracted hooks
  const processedData = useProcessedAssetData(assetStatistics, assetType);
  const btcStatistics = useBtcStatistics(assetStatistics, assetType);
  const filteredData = useFilteredAndSortedData({
    processedData,
    btcStatistics,
    filterConfig,
    sortConfig,
    assetType,
  });

  // Signal click handler
  const onSignalClick = useCallback(async (symbol: string, currency: string) => {
    try {
      await fetchChartData(symbol, currency);
      setShowChart(true);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to handle signal click:', error);
    }
  }, [fetchChartData]);

  // Refresh handler
  const onRefresh = useCallback(() => {
    void fetchData();
  }, [fetchData]);

  // Initial data fetch effect
  useEffect(() => {
    onRefresh();
  }, [onRefresh]);

  // Auto-refresh effect
  useEffect(() => {
    const REFRESH_INTERVAL = 30_000; // 30 seconds
    const interval = setInterval(() => {
      onRefresh();
    }, REFRESH_INTERVAL);

    return () => { clearInterval(interval); };
  }, [onRefresh]);

  return {
    // Processed data
    processedData: filteredData,
    btcStatistics,
    totalCount: processedData.length,
    filteredCount: filteredData.length,
    loading,
    error,

    // Chart data
    chartData: chartData as unknown,
    showChart,
    setShowChart,

    // Actions
    onSignalClick,
    onRefresh,

    // Utilities
    formatDate,
    findBtcDataForSymbol,
  };
};
